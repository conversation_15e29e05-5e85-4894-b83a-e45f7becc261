{"name": "reddit-za", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prisma:push": "prisma db push", "prisma:generate": "prisma generate", "seed": "ts-node prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@tanstack/react-query": "^5.84.1", "@uploadthing/react": "^7.3.2", "@uploadthing/shared": "^7.1.9", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "dayjs": "^1.11.13", "ioredis": "^5.7.0", "meilisearch": "^0.51.0", "next": "15.4.5", "next-auth": "^4.24.11", "next-intl": "^4.3.4", "prisma": "^6.13.0", "pusher-js": "^8.4.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "uploadthing": "^7.7.3", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}}
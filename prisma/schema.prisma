generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String       @id @default(cuid())
  email     String       @unique
  username  String       @unique
  password  String
  image     String?
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
  posts     Post[]
  comments  Comment[]
  votes     Vote[]
  karma     Int          @default(0)
  created   Community[]  @relation("UserToCommunities")
  memberships Membership[]
  moderations Moderator[]
}

model Community {
  id          String    @id @default(cuid())
  name        String    @unique
  title       String
  description String?
  createdAt   DateTime  @default(now())
  creator     User      @relation("UserToCommunities", fields: [creatorId], references: [id])
  creatorId   String
  members     Membership[]
  posts       Post[]
  mods        Moderator[]
}

model Membership {
  id          String    @id @default(cuid())
  user        User      @relation(fields: [userId], references: [id])
  userId      String
  community   Community @relation(fields: [communityId], references: [id])
  communityId String
  createdAt   DateTime  @default(now())

  @@unique([userId, communityId])
}

model Moderator {
  id          String    @id @default(cuid())
  user        User      @relation(fields: [userId], references: [id])
  userId      String
  community   Community @relation(fields: [communityId], references: [id])
  communityId String

  @@unique([userId, communityId])
}

model Post {
  id          String     @id @default(cuid())
  community   Community  @relation(fields: [communityId], references: [id])
  communityId String
  author      User       @relation(fields: [authorId], references: [id])
  authorId    String
  type        PostType
  title       String
  content     String?
  url         String?
  imageUrl    String?
  videoUrl    String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  comments    Comment[]
  votes       Vote[]
  score       Int        @default(0)
}

enum PostType {
  TEXT
  LINK
  IMAGE
  VIDEO
}

model Comment {
  id        String    @id @default(cuid())
  post      Post      @relation(fields: [postId], references: [id])
  postId    String
  author    User      @relation(fields: [authorId], references: [id])
  authorId  String
  parent    Comment?  @relation("CommentToComment", fields: [parentId], references: [id])
  parentId  String?
  children  Comment[] @relation("CommentToComment")
  content   String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  votes     Vote[]
}

model Vote {
  id        String   @id @default(cuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  post      Post?    @relation(fields: [postId], references: [id])
  postId    String?
  comment   Comment? @relation(fields: [commentId], references: [id])
  commentId String?
  value     Int

  @@unique([userId, postId, commentId])
}

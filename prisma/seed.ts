import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()

const communities = [
  { name: 'CapeTown', title: 'Cape Town', description: 'Mother City chats' },
  { name: 'Johannesburg', title: 'Johannesburg', description: 'Jozi life' },
  { name: '<PERSON><PERSON>', title: 'Durban', description: 'eThekwini vibes' },
  { name: 'Pretoria', title: 'Pretoria', description: 'Tshwane talk' },
  { name: 'PoliticsZA', title: 'Politics ZA', description: 'South African politics' },
  { name: 'BafanaBafana', title: '<PERSON><PERSON><PERSON> Bafana', description: 'Football' },
  { name: '<PERSON><PERSON><PERSON>', title: 'Springbo<PERSON>', description: 'Rugby' },
  { name: '<PERSON><PERSON><PERSON><PERSON>', title: '<PERSON>raaic<PERSON>', description: 'Braai<PERSON>leis, rugby, sunny skies' },
  { name: '<PERSON><PERSON><PERSON>', title: '<PERSON>ap<PERSON>', description: 'Music and culture' },
  { name: 'Afrikaans', title: 'Afrikaans', description: 'Afrikaans taal en kultuur' },
  { name: '<PERSON><PERSON><PERSON>', title: '<PERSON>hos<PERSON>', description: 'isi<PERSON>hos<PERSON>' },
  { name: '<PERSON><PERSON>', title: '<PERSON><PERSON>', description: 'isiZulu' }
]

async function main() {
  let systemUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } })
  if (!systemUser) {
    systemUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'system',
        password: 'disabled'
      }
    })
  }

  for (const c of communities) {
    await prisma.community.upsert({
      where: { name: c.name },
      update: {},
      create: {
        name: c.name,
        title: c.title,
        description: c.description,
        creatorId: systemUser.id
      }
    })
  }
}

main().finally(() => prisma.$disconnect())

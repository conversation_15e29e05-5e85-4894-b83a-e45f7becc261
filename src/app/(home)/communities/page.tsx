import { prisma } from '@/lib/prisma'
export default async function CommunitiesPage() {
  const communities = await prisma.community.findMany({ orderBy: { name: 'asc' } })
  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Communities (ZA)</h1>
      <ul className="space-y-2">
        {communities.map(c => (
          <li key={c.id} className="border rounded p-3">
            <div className="font-semibold">r/{c.name}</div>
            <div className="text-sm text-gray-500">{c.title}</div>
          </li>
        ))}
      </ul>
    </div>
  )
}
